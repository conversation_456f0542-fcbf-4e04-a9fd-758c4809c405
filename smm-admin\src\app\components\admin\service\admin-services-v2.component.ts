import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { CdkDrag, CdkDropList, CdkDragHandle, CdkDragPreview, CdkDragDrop } from '@angular/cdk/drag-drop';

import { IconDropdownComponent } from "../../common/icon-dropdown/icon-dropdown.component";
import { ServiceLabelComponent } from "../../common/service-label/service-label.component";
import { SocialIconComponent } from "../../common/social-icon/social-icon.component";
import { TimeFormatPipe } from '../../../core/pipes/time.pipe';
import { ToggleSwitchComponent } from "../../common/toggle-switch/toggle-switch.component";
import { AdminMenuComponent } from "../../common/admin-menu/admin-menu.component";

// Popup Components
import { NewServiceComponent } from '../../popup/new-service/new-service.component';
import { AddPlatformLightComponent } from '../../popup/add-platform-light/add-platform-light.component';
import { PlatformManagementComponent } from '../../platform-management/platform-management.component';
import { CategorySelectionComponent } from '../../popup/category-selection/category-selection.component';
import { NewCategoryComponent } from '../../popup/new-category/new-category.component';
import { NewPricesComponent } from '../../popup/new-prices/new-prices.component';
import { NewSpecialPricesComponent } from '../../popup/new-special-prices/new-special-prices.component';
import { SpecialPricesUserComponent } from '../../popup/special-prices-user/special-prices-user.component';
import { SpecialPricesServiceComponent } from '../../popup/special-prices-service/special-prices-service.component';
import { ImportServicesComponent } from '../../popup/import-services/import-services.component';
import { ResourcesComponent } from '../../popup/resources/resources.component';
import { DeleteConfirmationComponent } from '../../settings/delete-confirmation/delete-confirmation.component';

// Services
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { CategoryService } from '../../../core/services/category.service';
import { ServiceManagementService } from '../../../core/services/service-management.service';
import { FilterService } from '../../../core/services/filter.service';
import { SelectionService } from '../../../core/services/selection.service';
import { UIStateService } from '../../../core/services/ui-state.service';
import { PlatformService } from '../../../core/services/platform.service';
import { LoadingService } from '../../../core/services/loading.service';
import { ToastService } from '../../../core/services/toast.service';

// Models
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { SuperCategoryRes } from '../../../model/response/super-category.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { ExtendedCategoryRes } from '../../../model/extended/extended-category.model';
import { SpecialPriceRes } from '../../../model/response/special-price-res.model';
import { AddType } from '../../../constant/add-type';


@Component({
  selector: 'app-admin-services-v2',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FontAwesomeModule,
    TranslateModule,
    IconDropdownComponent,
    ServiceLabelComponent,
    SocialIconComponent,
    TimeFormatPipe,
    NewServiceComponent,
    AddPlatformLightComponent,
    PlatformManagementComponent,
    CategorySelectionComponent,
    NewCategoryComponent,
    NewPricesComponent,
    NewSpecialPricesComponent,
    SpecialPricesUserComponent,
    SpecialPricesServiceComponent,
    ImportServicesComponent,
    ResourcesComponent,
    DeleteConfirmationComponent,
    AdminMenuComponent,
    ToggleSwitchComponent,
    CdkDrag,
    CdkDropList,
    CdkDragHandle,
    CdkDragPreview
  ],
  templateUrl: './admin-services-v2.component.html',
  styleUrls: ['./admin-services-v2.component.css', '../common/admin-menu.css']
})
export class AdminServicesV2Component implements OnInit, OnDestroy {
  allPlatforms: SuperPlatformRes[] = [];

  // View mode for mobile/desktop
  viewMode: 'table' | 'card' = 'table';

  // Category platform dropdown properties
  categoryPlatformSelections: Map<number, Set<number>> = new Map<number, Set<number>>();
  get showCategoryPlatformDropdown(): boolean { return this.uiStateService.showCategoryPlatformDropdown; }
  get selectedCategoryForPlatform(): ExtendedCategoryRes | null { return this.uiStateService.selectedCategoryForPlatform; }
  get categoryPlatformMenuPosition(): { top: number, left: number } { return this.uiStateService.categoryPlatformMenuPosition; }

  // Category options for dropdown
  categoryOptions: any[] = [];
  selectedCategory: any = null;

  // Display categories and current category
  displayCategories: ExtendedCategoryRes[] = [];
  currentCategory: ExtendedCategoryRes | null = null;

  // Unfiltered state for reset functionality
  unfilteredDisplayCategories: ExtendedCategoryRes[] = [];
  unfilteredCurrentCategory: ExtendedCategoryRes | null = null;

  // Filter state
  isFilterApplied = false;
  searchTerm = '';

  // Resources modal
  showResourcesModal = false;

  // Delete confirmation
  showDeleteConfirmation = false;
  serviceToDelete: SuperGeneralSvRes | null = null;
  isDeleting = false;

  // Drag and drop state
  draggedCategory: ExtendedCategoryRes | null = null;
  draggedCategoryIndex: number = -1;
  dragOverIndex: number = -1;

  constructor(
    private adminServiceService: AdminServiceService,
    private categoryService: CategoryService,
    private serviceManagementService: ServiceManagementService,
    private filterService: FilterService,
    public selectionService: SelectionService,
    public uiStateService: UIStateService,
    private platformService: PlatformService,
    private loadingService: LoadingService,
    private toastService: ToastService
  ) {}

  // Getters for UI state
  get showModal(): boolean { return this.uiStateService.showModal; }
  get showAddServiceV2Modal(): boolean { return this.uiStateService.showAddServiceV2Modal; }
  get showAddPlatformLightModal(): boolean { return this.uiStateService.showAddPlatformLightModal; }
  get showPlatformManagementModal(): boolean { return this.uiStateService.showPlatformManagementModal; }
  get showCategorySelectionModal(): boolean { return this.uiStateService.showCategorySelectionModal; }
  get showNewCategoryModal(): boolean { return this.uiStateService.showNewCategoryModal; }
  get showNewPricesModal(): boolean { return this.uiStateService.showNewPricesModal; }
  get showNewSpecialPricesModal(): boolean { return this.uiStateService.showNewSpecialPricesModal; }
  get showSpecialPricesUserModal(): boolean { return this.uiStateService.showSpecialPricesUserModal; }
  get showSpecialPricesServiceModal(): boolean { return this.uiStateService.showSpecialPricesServiceModal; }
  get showImportServicesModal(): boolean { return this.uiStateService.showImportServicesModal; }
  get showNewServiceModal(): boolean { return this.uiStateService.showNewServiceModal; }

  // Selected items for actions
  get selectedServiceForAction(): SuperGeneralSvRes | null { return this.uiStateService.selectedServiceForAction; }
  get selectedCategoryForAction(): ExtendedCategoryRes | null { return this.uiStateService.selectedCategoryForAction; }
  get categoryToEdit(): any | null { return this.uiStateService.categoryToEdit; }
  set categoryToEdit(value: any | null) { this.uiStateService.categoryToEdit = value; }
  get specialPriceToEdit(): any | null { return this.uiStateService.specialPriceToEdit; }

  // Menu positions
  get menuPosition(): { top: number, left: number } { return this.uiStateService.menuPosition; }
  get categoryMenuPosition(): { top: number, left: number } { return this.uiStateService.categoryMenuPosition; }

  // Menu states
  get showBulkActionMenu(): boolean { return this.uiStateService.showBulkActionMenu; }
  get showServiceActionMenu(): boolean { return this.uiStateService.showServiceActionMenu; }
  get showCategoryActionMenu(): boolean { return this.uiStateService.showCategoryActionMenu; }

  // Loading states
  get loading(): boolean { return this.uiStateService.loading; }
  get loadingBulkOperation(): boolean { return this.uiStateService.loadingBulkOperation; }
  get bulkOperationMessage(): string { return this.uiStateService.bulkOperationMessage; }

  ngOnInit(): void {
    this.loadPlatforms();
    this.detectMobileDevice();

    // Listen for window resize events to update view mode
    window.addEventListener('resize', () => {
      this.detectMobileDevice();
    });

    // Close menus when scrolling
    window.addEventListener('scroll', () => {
      this.closeAllMenus();
    }, true);
  }

  ngOnDestroy(): void {
    // Remove event listeners
    window.removeEventListener('resize', () => {
      this.detectMobileDevice();
    });

    window.removeEventListener('scroll', () => {
      this.closeAllMenus();
    }, true);
  }

  // Close all menus
  closeAllMenus(): void {
    this.uiStateService.closeBulkActionMenu();
    this.uiStateService.closeServiceActionMenu();
    this.uiStateService.closeCategoryActionMenu();
  }

  /**
   * Detect if the device is mobile and set the view mode accordingly
   */
  detectMobileDevice(): void {
    // Check if screen width is less than 768px (typical mobile breakpoint)
    if (window.innerWidth < 768) {
      this.viewMode = 'card';
    } else {
      this.viewMode = 'table';
    }
  }

  loadPlatforms(): void {
    this.uiStateService.setLoading(true, 'Loading platforms and services...');
    this.loadingService.show('Loading platforms and services...');

    // Use PlatformService to load platforms with services
    this.platformService.loadPlatformsWithServices(
      (platforms) => {
        // Store all platforms data
        this.allPlatforms = platforms;

        // Create a default "All Categories" option
        const allCategoriesOption = {
          id: 'all',
          label: 'filter.all_categories',
          sort: 0,
          icon: '' // No icon
        };

        // Create category options from platforms
        this.categoryOptions = [allCategoriesOption];

        // Add individual categories from all platforms
        platforms.forEach(platform => {
          if (platform.categories && platform.categories.length > 0) {
            platform.categories.forEach(category => {
              this.categoryOptions.push({
                id: category.id,
                label: category.name,
                sort: category.sort || 0,
                icon: platform.icon || ''
              });
            });
          }
        });

        // Sort category options by sort field
        this.categoryOptions.sort((a, b) => a.sort - b.sort);

        // Set default selected category to "All Categories"
        this.selectedCategory = allCategoriesOption;

        // Process platforms data for display
        this.processDisplayCategories(platforms);

        this.uiStateService.setLoading(false);
        this.loadingService.hide();
      },
      (error) => {
        console.error('Error loading platforms:', error);
        this.uiStateService.setLoading(false);
        this.loadingService.hide();
        this.toastService.showError('Failed to load platforms and services');
      }
    );
  }

  processDisplayCategories(platforms: SuperPlatformRes[]): void {
    this.displayCategories = [];

    platforms.forEach(platform => {
      if (platform.categories && platform.categories.length > 0) {
        platform.categories.forEach(category => {
          const extendedCategory: ExtendedCategoryRes = {
            ...category,
            // platformId: platform.id,
            platformName: platform.name,
            platformIcon: platform.icon,
            isAllPlatforms: false,
            isAllCategories: false,
            hide: false,
            services: category.services || []
          };

          this.displayCategories.push(extendedCategory);
        });
      }
    });

    // Sort categories by sort field
    this.displayCategories.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  }

  // Category selection methods
  onCategorySelected(category: any): void {
    this.selectedCategory = category;

    if (category.id === 'all') {
      // Show all categories
      this.currentCategory = null;
    } else {
      // Find and show specific category
      const foundCategory = this.displayCategories.find(cat => cat.id === category.id);
      this.currentCategory = foundCategory || null;
    }
  }

  // Filter methods
  applyFilter(searchText: string): void {
    const result = this.filterService.applyFilter(
      searchText,
      this.displayCategories,
      this.currentCategory,
      this.unfilteredDisplayCategories,
      this.unfilteredCurrentCategory
    );

    this.displayCategories = result.displayCategories;
    this.currentCategory = result.currentCategory;
    this.unfilteredDisplayCategories = result.unfilteredDisplayCategories;
    this.unfilteredCurrentCategory = result.unfilteredCurrentCategory;
    this.isFilterApplied = result.isFilterApplied;
    this.searchTerm = result.searchTerm;
  }

  resetFilter(searchInput: HTMLInputElement): void {
    // Clear search input
    searchInput.value = '';

    // Reset filter state
    if (this.isFilterApplied) {
      this.displayCategories = [...this.unfilteredDisplayCategories];
      this.currentCategory = this.unfilteredCurrentCategory;
      this.unfilteredDisplayCategories = [];
      this.unfilteredCurrentCategory = null;
      this.isFilterApplied = false;
      this.searchTerm = '';
    }
  }

  // Service selection methods
  isServiceSelected(service: SuperGeneralSvRes): boolean {
    return this.selectionService.isServiceSelected(service);
  }

  toggleServiceSelection(service: SuperGeneralSvRes, event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleServiceSelection(service, checkbox.checked);
  }

  areAllServicesSelected(): boolean {
    return this.selectionService.areAllServicesSelected(this.displayCategories, this.currentCategory);
  }

  toggleAllServices(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleAllServices(this.displayCategories, this.currentCategory, checkbox.checked);
  }

  hasSelectedServices(): boolean {
    return this.selectionService.hasSelectedServices();
  }

  getSelectedServicesCount(): number {
    return this.selectionService.getSelectedServicesCount();
  }

  getAllServices(): SuperGeneralSvRes[] {
    const allServices: SuperGeneralSvRes[] = [];

    if (this.currentCategory) {
      allServices.push(...this.currentCategory.services);
    } else {
      this.displayCategories.forEach(category => {
        allServices.push(...category.services);
      });
    }

    return allServices;
  }

  // Category selection methods
  isCategoryFullySelected(category: ExtendedCategoryRes): boolean {
    return this.selectionService.isCategoryFullySelected(category);
  }

  toggleCategorySelection(category: ExtendedCategoryRes, event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleCategorySelection(category, checkbox.checked);
  }

  // Service status methods
  isServiceEnabled(service: SuperGeneralSvRes): boolean {
    return service.status === 'ACTIVATED';
  }

  toggleServiceStatus(service: SuperGeneralSvRes): void {
    if (this.isServiceEnabled(service)) {
      this.adminServiceService.deactivateService(service.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Service deactivated successfully');
          this.loadPlatforms();
        },
        error: (error) => {
          console.error('Error deactivating service:', error);
          this.toastService.showError('Failed to deactivate service');
        }
      });
    } else {
      this.adminServiceService.activateService(service.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Service activated successfully');
          this.loadPlatforms();
        },
        error: (error) => {
          console.error('Error activating service:', error);
          this.toastService.showError('Failed to activate service');
        }
      });
    }
  }

  // Modal methods
  openModal(): void {
    this.uiStateService.openModal();
  }

  closeModal(): void {
    this.uiStateService.closeModal();
  }

  closeAddServiceV2Modal(): void {
    this.uiStateService.closeAddServiceV2Modal();
    this.loadPlatforms();
  }

  closeAddPlatformLightModal(): void {
    this.uiStateService.closeAddPlatformLightModal();
    this.loadPlatforms();
  }

  closePlatformManagementModal(): void {
    this.uiStateService.closePlatformManagementModal();
    this.loadPlatforms();
  }

  closeCategorySelectionModal(): void {
    this.uiStateService.closeCategorySelectionModal();
  }

  closeNewCategoryModal(): void {
    this.uiStateService.closeNewCategoryModal();
    this.loadPlatforms();
  }

  closeNewPricesModal(): void {
    this.uiStateService.closeNewPricesModal();
    this.loadPlatforms();
  }

  closeNewSpecialPricesModal(): void {
    this.uiStateService.closeNewSpecialPricesModal();
    this.loadPlatforms();
  }

  closeSpecialPricesUserModal(): void {
    this.uiStateService.closeSpecialPricesUserModal();
    this.loadPlatforms();
  }

  closeSpecialPricesServiceModal(): void {
    this.uiStateService.closeSpecialPricesServiceModal();
    this.loadPlatforms();
  }

  closeImportServicesModal(): void {
    this.uiStateService.closeImportServicesModal();
    this.loadPlatforms();
  }

  // Action button methods
  addService(): void {
    this.showResourcesModal = true;
  }

  importServices(): void {
    this.uiStateService.openImportServicesModal();
  }

  openNewCategoryModal(): void {
    this.categoryToEdit = null;
    this.uiStateService.openNewCategoryModal();
  }

  // Menu item methods
  getBulkActionMenuItems(): any[] {
    const items = [];

    // Enable all
    items.push({
      id: 'enable-all',
      label: 'Enable All',
      icon: 'check',
      iconColor: 'text-green-600'
    });

    // Disable all
    items.push({
      id: 'disable-all',
      label: 'Disable All',
      icon: 'times',
      iconColor: 'text-red-600'
    });

    // Change prices
    items.push({
      id: 'change-prices',
      label: 'Change Prices',
      icon: 'dollar-sign',
      iconColor: 'text-blue-600'
    });

    // Special prices
    items.push({
      id: 'special-prices',
      label: 'Special Prices',
      icon: 'star',
      iconColor: 'text-yellow-600'
    });

    return items;
  }

  onBulkActionMenuItemClick(itemId: string): void {
    switch (itemId) {
      case 'enable-all':
        this.enableAllSelected();
        break;
      case 'disable-all':
        this.disableAllSelected();
        break;
      case 'change-prices':
        this.changePricesForSelected();
        break;
      case 'special-prices':
        this.addSpecialPricesForSelected();
        break;
    }
  }

  getCategoryMenuItems(_category: ExtendedCategoryRes): any[] {
    const items = [];

    // Edit category
    items.push({
      id: 'edit-category',
      label: 'Edit Category',
      icon: 'edit',
      iconColor: 'text-blue-600'
    });

    // Add service to category
    items.push({
      id: 'add-service',
      label: 'Add Service',
      icon: 'plus',
      iconColor: 'text-green-600'
    });

    return items;
  }

  onCategoryMenuItemClick(itemId: string, category: ExtendedCategoryRes): void {
    switch (itemId) {
      case 'edit-category':
        this.editCategory(category);
        break;
      case 'add-service':
        this.addServiceToCategory(category);
        break;
    }
  }

  getServiceMenuItems(_service: SuperGeneralSvRes): any[] {
    const items = [];

    // Notify resellers
    items.push({
      id: 'notify-resellers',
      label: 'Notify Resellers',
      icon: 'bell',
      iconColor: 'text-blue-600'
    });

    // Edit service
    items.push({
      id: 'edit-service',
      label: 'Edit Service',
      icon: 'edit',
      iconColor: 'text-blue-600'
    });

    // Change category
    items.push({
      id: 'change-category',
      label: 'Change Category',
      icon: 'folder',
      iconColor: 'text-orange-600'
    });

    // Duplicate
    items.push({
      id: 'duplicate',
      label: 'Duplicate',
      icon: 'copy',
      iconColor: 'text-green-600'
    });

    // Special prices
    items.push({
      id: 'special-prices',
      label: 'Special Prices',
      icon: 'star',
      iconColor: 'text-yellow-600'
    });

    // Delete
    items.push({
      id: 'delete',
      label: 'Delete',
      icon: 'trash',
      iconColor: 'text-red-600'
    });

    return items;
  }

  onServiceMenuItemClick(itemId: string, service: SuperGeneralSvRes): void {
    switch (itemId) {
      case 'notify-resellers':
        this.notifyResellers(service);
        break;
      case 'edit-service':
        this.editService(service);
        break;
      case 'change-category':
        this.changeServiceCategory(service);
        break;
      case 'duplicate':
        this.duplicateService(service);
        break;
      case 'special-prices':
        this.addSpecialPriceForService(service);
        break;
      case 'delete':
        this.deleteService(service);
        break;
    }
  }

  // Bulk action implementations
  enableAllSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }

    this.uiStateService.setBulkOperationLoading(true, `Enabling ${selectedServices.size} services...`);
    this.serviceManagementService.enableAllSelected(selectedServices, (completedCount, errorCount) => {
      this.uiStateService.setBulkOperationLoading(false);
      this.toastService.showSuccess(`Enabled ${completedCount} services`);
      if (errorCount > 0) {
        this.toastService.showError(`Failed to enable ${errorCount} services`);
      }
    });
  }

  disableAllSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }

    this.uiStateService.setBulkOperationLoading(true, `Disabling ${selectedServices.size} services...`);
    this.serviceManagementService.disableAllSelected(selectedServices, (completedCount, errorCount) => {
      this.uiStateService.setBulkOperationLoading(false);
      this.toastService.showSuccess(`Disabled ${completedCount} services`);
      if (errorCount > 0) {
        this.toastService.showError(`Failed to disable ${errorCount} services`);
      }
    });
  }

  changePricesForSelected(): void {
    this.uiStateService.openNewPricesModal();
  }

  addSpecialPricesForSelected(): void {
    this.uiStateService.openNewSpecialPricesModal();
  }

  // Category action implementations
  editCategory(category: ExtendedCategoryRes): void {
    this.categoryToEdit = category;
    this.uiStateService.openNewCategoryModal();
  }

  addServiceToCategory(category: ExtendedCategoryRes): void {
    this.uiStateService.selectedCategoryForAction = category;
    this.showResourcesModal = true;
  }

  // Service action implementations
  notifyResellers(_service: SuperGeneralSvRes): void {
    this.toastService.showSuccess('Notifying resellers...');
    // Implementation for notifying resellers
  }

  editService(service: SuperGeneralSvRes): void {
    const serviceCopy = JSON.parse(JSON.stringify(service));
    this.uiStateService.selectedServiceForAction = serviceCopy;
    const serviceType = serviceCopy.add_type === AddType.Api ? 'Provider' : 'Manual';
    this.uiStateService.openNewServiceModalForEdit(serviceCopy, serviceType);
  }

  changeServiceCategory(service: SuperGeneralSvRes): void {
    this.uiStateService.selectedServiceForAction = service;
    this.uiStateService.openCategorySelectionModal();
  }

  duplicateService(_service: SuperGeneralSvRes): void {
    this.toastService.showSuccess('Duplicating service...');
    // Implementation for duplicating service
  }

  addSpecialPriceForService(service: SuperGeneralSvRes): void {
    this.uiStateService.selectedServiceForAction = service;
    this.uiStateService.openSpecialPricesServiceModal();
  }

  deleteService(service: SuperGeneralSvRes): void {
    this.serviceToDelete = service;
    this.showDeleteConfirmation = true;
  }

  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.serviceToDelete = null;
  }

  confirmDeleteService(): void {
    if (!this.serviceToDelete) return;

    this.isDeleting = true;
    this.adminServiceService.deleteService(this.serviceToDelete.id).subscribe({
      next: () => {
        this.toastService.showSuccess('Service deleted successfully');
        this.closeDeleteConfirmation();
        this.loadPlatforms();
      },
      error: (error) => {
        console.error('Error deleting service:', error);
        this.toastService.showError('Failed to delete service');
        this.isDeleting = false;
      }
    });
  }

  // Drag and drop methods for categories
  onCategoryDragStart(event: DragEvent, category: ExtendedCategoryRes, index: number): void {
    this.draggedCategory = category;
    this.draggedCategoryIndex = index;

    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/html', '');
    }
  }

  onCategoryDragOver(event: DragEvent, index: number): void {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
    this.dragOverIndex = index;
  }

  onCategoryDragEnter(event: DragEvent): void {
    event.preventDefault();
  }

  onCategoryDragLeave(_event: DragEvent): void {
    // Reset drag over index when leaving
    this.dragOverIndex = -1;
  }

  onCategoryDrop(event: DragEvent, dropIndex: number): void {
    event.preventDefault();

    if (this.draggedCategory && this.draggedCategoryIndex !== -1) {
      // Reorder categories
      const categories = [...this.displayCategories];
      const draggedItem = categories.splice(this.draggedCategoryIndex, 1)[0];
      categories.splice(dropIndex, 0, draggedItem);

      this.displayCategories = categories;

      // Update sort order in backend
      this.updateCategoryOrder();
    }

    this.resetDragState();
  }

  onCategoryDragEnd(_event: DragEvent): void {
    this.resetDragState();
  }

  resetDragState(): void {
    this.draggedCategory = null;
    this.draggedCategoryIndex = -1;
    this.dragOverIndex = -1;
  }

  updateCategoryOrder(): void {
    // Implementation to update category order in backend
    this.toastService.showSuccess('Updating category order...');
  }

  // Drag and drop methods for services
  onServiceDropCdk(event: CdkDragDrop<SuperGeneralSvRes[]>, category: ExtendedCategoryRes): void {
    if (event.previousContainer === event.container) {
      // Reorder within same category
      const services = [...category.services];
      const movedService = services.splice(event.previousIndex, 1)[0];
      services.splice(event.currentIndex, 0, movedService);
      category.services = services;

      this.updateServiceOrder(category);
    } else {
      // Move between categories
      const previousCategory = this.findCategoryByServices(event.previousContainer.data);
      if (previousCategory) {
        const movedService = previousCategory.services.splice(event.previousIndex, 1)[0];
        category.services.splice(event.currentIndex, 0, movedService);

        this.moveServiceToCategory(movedService, category);
      }
    }
  }

  onServiceDragStarted(_event: any): void {
    // Handle service drag start
  }

  findCategoryByServices(services: SuperGeneralSvRes[]): ExtendedCategoryRes | null {
    return this.displayCategories.find(cat => cat.services === services) || null;
  }

  updateServiceOrder(_category: ExtendedCategoryRes): void {
    // Implementation to update service order in backend
    this.toastService.showSuccess('Updating service order...');
  }

  moveServiceToCategory(_service: SuperGeneralSvRes, targetCategory: ExtendedCategoryRes): void {
    // Implementation to move service to different category
    this.toastService.showSuccess(`Moving service to ${targetCategory.name}...`);
  }

  // Category platform dropdown methods
  toggleCategoryPlatformDropdown(event: MouseEvent, category: ExtendedCategoryRes): void {
    event.stopPropagation();

    if (this.showCategoryPlatformDropdown && this.selectedCategoryForPlatform?.id === category.id) {
      this.uiStateService.closeCategoryPlatformDropdown();
      return;
    }

    this.uiStateService.selectedCategoryForPlatform = category;
    this.uiStateService.showCategoryPlatformDropdown = true;

    // Set menu position
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    this.uiStateService.categoryPlatformMenuPosition = {
      top: rect.bottom + window.scrollY,
      left: rect.left + window.scrollX
    };
  }

  selectCategoryPlatform(category: ExtendedCategoryRes, platform: SuperPlatformRes): void {
    // Toggle platform selection for category
    if (!this.categoryPlatformSelections.has(category.id)) {
      this.categoryPlatformSelections.set(category.id, new Set());
    }

    const selections = this.categoryPlatformSelections.get(category.id)!;
    if (selections.has(platform.id)) {
      selections.delete(platform.id);
    } else {
      selections.add(platform.id);
    }

    this.toastService.showSuccess(`Platform ${platform.name} ${selections.has(platform.id) ? 'added to' : 'removed from'} ${category.name}`);
  }

  isCategoryPlatformSelected(category: ExtendedCategoryRes, platformId: number): boolean {
    const selections = this.categoryPlatformSelections.get(category.id);
    return selections ? selections.has(platformId) : false;
  }

  addNewPlatformFromCategory(event: MouseEvent): void {
    event.stopPropagation();
    this.uiStateService.closeCategoryPlatformDropdown();
    this.uiStateService.openPlatformManagementModal();
  }

  // Category visibility toggle
  onToggleCategoryVisibility(category: ExtendedCategoryRes, currentHideState: boolean): void {
    category.hide = !currentHideState;
  }

  // Utility methods
  extractDomainName(url: string): string {
    if (!url) return '';
    try {
      const domain = url.replace(/^https?:\/\//, '').split('/')[0];
      return domain;
    } catch {
      return url;
    }
  }

  copyToClipboard(text: string, event: MouseEvent): void {
    event.stopPropagation();
    navigator.clipboard.writeText(text).then(() => {
      this.toastService.showSuccess('Copied to clipboard');
    }).catch(() => {
      this.toastService.showError('Failed to copy');
    });
  }

  // Event handlers for modal callbacks
  onServiceAdded(_service: SuperGeneralSvRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Service added successfully');
  }

  onPlatformAdded(_platform: SuperPlatformRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Platform added successfully');
  }

  onPlatformsUpdated(_platforms: SuperPlatformRes[]): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Platforms updated successfully');
  }

  onCategoryAdded(_category: SuperCategoryRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Category added successfully');
  }

  onCategoryUpdated(_category: SuperCategoryRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Category updated successfully');
  }

  onCategorySelectedForMove(category: SuperCategoryRes): void {
    if (this.selectedServiceForAction) {
      this.moveServiceToCategory(this.selectedServiceForAction, category as any);
    }
  }

  onPricesUpdated(_services: SuperGeneralSvRes[]): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Prices updated successfully');
  }

  onSpecialPriceAdded(_specialPrice: SpecialPriceRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Special price added successfully');
  }

  onServicesImported(services: SuperGeneralSvRes[]): void {
    this.loadPlatforms();
    this.toastService.showSuccess(`${services.length} services imported successfully`);
  }

  // Resources modal methods
  closeResourcesModal(): void {
    this.showResourcesModal = false;
  }

  openNewServiceFromResources(serviceType: string): void {
    this.showResourcesModal = false;
    this.uiStateService.openNewServiceModal(serviceType);
  }

  closeNewServiceModal(): void {
    this.uiStateService.closeNewServiceModal();
  }

  openResourcesFromNewService(): void {
    this.showResourcesModal = true;
  }
}
