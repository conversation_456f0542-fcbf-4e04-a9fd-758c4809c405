/* Admin container */
.admin-container {
  @apply w-full bg-white rounded-lg md:p-6 p-2;
}

/* Admin header */
.admin-header {
  @apply flex justify-between items-center mb-6;
}

.admin-title {
  @apply text-xl font-bold uppercase;
}

/* Search and filter container */
.search-filter-container {
  @apply mb-6;
}

/* Search input */
.search-input-wrapper {
  @apply relative flex items-center w-full;
}

.search-input {
  @apply w-full h-[52px] px-4 py-2 bg-[#f5f7fc] rounded-lg border-none focus:outline-none focus:ring-2 focus:ring-[var(--primary)];
}

.search-button {
  @apply absolute right-2 bg-[var(--primary)] text-white p-2 rounded-md;
}

/* Category container */
.category-container {
  @apply mb-8 bg-white rounded-lg overflow-hidden;
}

/* Category header */
.category-header-row {
  @apply bg-gray-50 border-b border-gray-200;
}

.category-header-row:hover {
  @apply bg-gray-100;
}

.category-header-row.drag-over {
  @apply bg-blue-50 border-blue-200;
}

.header-left {
  @apply p-3 bg-gray-50;
}

/* Category drag handle */
.category-drag-handle {
  @apply cursor-grab;
}

.category-drag-handle:active {
  @apply cursor-grabbing;
}

/* Category visibility toggle */
.category-visibility-toggle {
  @apply flex items-center;
}

.toggle-arrow-btn {
  @apply p-1 rounded hover:bg-gray-200 transition-colors duration-200;
}

/* Platform dropdown */
.platform-dropdown-content {
  @apply absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50;
}

/* Service rows */
.service-row {
  @apply border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200;
}

.service-row.deactivated {
  @apply opacity-50 bg-gray-100;
}

.service-row.cdk-drag-preview {
  @apply shadow-lg border border-gray-300 rounded-lg bg-white;
}

/* Service drag handle */
.service-drag-handle {
  @apply cursor-grab text-gray-400 hover:text-gray-600;
}

.service-drag-handle:active {
  @apply cursor-grabbing;
}

/* API info column */
.api-info-column {
  @apply space-y-1;
}

.api-id-container {
  @apply flex items-center space-x-2;
}

.api-id {
  @apply text-sm font-mono text-gray-600;
}

.api-url-container {
  @apply flex items-center space-x-2;
}

.api-url {
  @apply text-sm text-blue-600 hover:text-blue-800 underline;
}

.copy-icon {
  @apply text-gray-400 hover:text-gray-600 cursor-pointer text-xs;
}

/* Refill badge */
.lifetime-refill-badge {
  @apply inline-block px-2 py-1 text-xs font-medium rounded-full;
  @apply bg-green-100 text-green-800;
}

/* Form elements */
.form-checkbox {
  @apply h-4 w-4 text-[var(--primary)] focus:ring-[var(--primary)] border-gray-300 rounded;
}

/* Table styles */
.admin-services-table {
  @apply min-w-full divide-y divide-gray-200;
}

.admin-services-table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.admin-services-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Drag and drop styles */
.cdk-drop-list {
  @apply block;
}

.cdk-drag {
  @apply transition-transform duration-200;
}

.cdk-drag-preview {
  @apply shadow-lg rounded-lg;
}

.cdk-drag-placeholder {
  @apply opacity-0;
}

.cdk-drag-animating {
  @apply transition-transform duration-300;
}

/* Service preview table */
.service-preview-table {
  @apply w-full border-collapse;
}

.service-row-preview {
  @apply bg-white shadow-lg rounded-lg;
}

.service-row-preview td {
  @apply px-4 py-2 text-sm;
}

/* Category preview table */
.category-preview-table {
  @apply w-full border-collapse;
}

.category-header-row-preview {
  @apply bg-gray-50 shadow-lg rounded-lg;
}

.category-header-row-preview td {
  @apply px-4 py-2;
}

/* Menu container */
.menu-container {
  @apply relative inline-block;
}

/* Loading states */
.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)];
}

/* Mobile responsive */
@media (max-width: 768px) {
  .admin-container {
    @apply p-2;
  }

  .admin-services-table {
    @apply text-xs;
  }

  .admin-services-table th,
  .admin-services-table td {
    @apply px-2 py-2;
  }
}

/* Card view styles */
.service-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4;
}

.service-card:hover {
  @apply shadow-md;
}

.service-card.selected {
  @apply border-[var(--primary)] bg-blue-50;
}

.service-card.deactivated {
  @apply opacity-50 bg-gray-100;
}

/* Bulk action styles */
.bulk-action-header {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4;
}

.bulk-action-count {
  @apply font-medium text-blue-800;
}

/* Animation classes */
.fade-in {
  animation: fade-in 0.3s ease-in-out;
}

.slide-up {
  animation: slide-up 0.3s ease-in-out;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-up {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Status indicators */
.status-active {
  @apply text-green-600;
}

.status-inactive {
  @apply text-red-600;
}

.status-pending {
  @apply text-yellow-600;
}

/* Button variants */
.btn-primary {
  @apply bg-[var(--primary)] text-white font-medium px-4 py-2 rounded-lg hover:bg-[var(--primary-hover)] transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 font-medium px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 text-white font-medium px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200;
}

/* Utility classes */
.text-truncate {
  @apply truncate;
}

.text-wrap {
  @apply break-words;
}

.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}
