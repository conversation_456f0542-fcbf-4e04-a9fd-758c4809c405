
<div class="admin-container">

<!-- Add service and Import services buttons -->
<div class="flex justify-between items-center gap-3 mb-4">
  <div class="flex gap-3">
    <button (click)="addService()" class="bg-[var(--primary)] text-white font-medium text-sm px-6 py-3 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-blue-600 active:bg-blue-700">
      {{ 'admin.services.add_service' | translate }}
    </button>
    <button (click)="importServices()" class="bg-transparent border border-[var(--primary)] text-[var(--primary)] font-medium text-sm px-6 py-3 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-[#0095f6]/10 active:bg-[#0095f6]/20">
      {{ 'admin.services.import_services' | translate }}
    </button>
  </div>

  <!-- View mode toggle -->
  <!-- <div class="viewMode-toggle flex items-center bg-gray-100 rounded-lg p-1">
    <button
      (click)="viewMode = 'table'"
      [ngClass]="{'bg-white shadow-sm': viewMode === 'table', 'text-gray-500': viewMode !== 'table'}"
      class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200">
      <fa-icon [icon]="['fas', 'table']" class="mr-1"></fa-icon>
      {{ 'Table' | translate }}
    </button>
    <button
      (click)="viewMode = 'card'"
      [ngClass]="{'bg-white shadow-sm': viewMode === 'card', 'text-gray-500': viewMode !== 'card'}"
      class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200">
      <fa-icon [icon]="['fas', 'th-large']" class="mr-1"></fa-icon>
      {{ 'Cards' | translate }}
    </button>
  </div> -->
</div>

<div class="flex flex-col md:flex-row gap-4 mb-4 bg-white rounded-2xl">
  <div class="flex-1 md:flex-1">
      <div class="text-gray-900 font-medium text-sm mb-1">{{ 'search' | translate }}</div>
      <input type="text" #searchInput placeholder="{{ 'search' | translate }}" class="border-none w-full bg-[#f5f7fc] rounded-lg" style="height: 52px;">
  </div>


  <!-- Category Section -->
  <div class="flex-1 md:flex-1">
      <div class="text-gray-900 font-medium text-sm mb-1">{{ 'category' | translate }}</div>
      <div class="relative">
        <app-icon-dropdown
            [options]="categoryOptions"
            [selectedOption]="selectedCategory"
            (selected)="onCategorySelected($event)"
            [customClassDropdown]="'bg-[#f5f7fc] rounded-lg '"
        ></app-icon-dropdown>
        <button (click)="openNewCategoryModal()" class="absolute right-0 top-0 bg-[var(--primary)] text-white font-medium text-sm px-3 rounded-r-lg cursor-pointer transition-colors duration-300 hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)]" style="height: 52px;">
            <fa-icon [icon]="['fas', 'plus']" class="mr-1"></fa-icon>
            {{ 'Add' | translate }}
        </button>
      </div>
  </div>

  <!-- button Section -->
  <div class="flex gap-2 items-end">
      <button (click)="applyFilter(searchInput.value)" class="bg-[var(--primary)] text-white font-medium text-sm px-4 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)]" style="height: 52px;">
          {{ 'filter.button' | translate }}
      </button>
      <button (click)="resetFilter(searchInput)" class="bg-gray-100 text-gray-700 font-medium text-sm px-4 rounded-lg cursor-pointer transition-colors duration-300 hover:bg-gray-200 active:bg-gray-300" style="height: 52px;">
          {{ 'reset' | translate }}
      </button>


  </div>
</div>



<!-- Table View (for desktop) -->
<div class="table mb-6" *ngIf="viewMode === 'table'" style="width: 100%!important; max-width: none!important;">
  <table class="w-full divide-y divide-gray-200 admin-services-table" style="table-layout: fixed!important; width: 100%!important; min-width: 100%!important; max-width: none!important;">
    <!-- <colgroup>
      <col style="width: 50px;">
      <col style="width: 30%;">
      <col style="width: 20%;">
      <col style="width: 80px;">
      <col style="width: 80px;">
      <col style="width: 120px;">
      <col style="width: 120px;">
      <col style="width: 60px;">
    </colgroup> -->
    <thead>
      <tr>
        <th class="text-center">
          <input
            type="checkbox"
            [checked]="areAllServicesSelected()"
            (change)="toggleAllServices($event)"
            class="form-checkbox"
          >
        </th>
        <th>
          <div class="flex items-center">
            <ng-container *ngIf="hasSelectedServices(); else normalServiceHeader">
              <span class="font-medium">{{ getSelectedServicesCount() }} {{ 'selected' | translate }}</span>
              <div class="relative ml-2">
                <app-admin-menu
                  [menuItems]="getBulkActionMenuItems()"
                  (menuItemClicked)="onBulkActionMenuItemClick($event)">
                  <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                </app-admin-menu>
              </div>
            </ng-container>
            <ng-template #normalServiceHeader>
              {{ 'service' | translate }}
            </ng-template>
          </div>
        </th>
        <th>{{ 'api_info' | translate }}</th>
        <th>{{ 'min_value' | translate }}</th>
        <th>{{ 'max_value' | translate }}</th>

        <th>{{ 'refill' | translate }}</th>
        <th>{{ 'average_time' | translate }}</th>
        <th>{{ 'action' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <!-- Display multiple categories when "All Categories" is selected -->
      <ng-container *ngIf="displayCategories.length > 0">
        <ng-container *ngFor="let category of displayCategories">
          <!-- Category Header Row -->
          <tr class="category-header-row"
              draggable="true"
              (dragstart)="onCategoryDragStart($event, category, displayCategories.indexOf(category))"
              (dragover)="onCategoryDragOver($event, displayCategories.indexOf(category))"
              (dragenter)="onCategoryDragEnter($event)"
              (dragleave)="onCategoryDragLeave($event)"
              (drop)="onCategoryDrop($event, displayCategories.indexOf(category))"
              (dragend)="onCategoryDragEnd($event)">
            <td colspan="9" (dragover)="onDragOver($event, category.id.toString(), category.services.length)"
            (drop)="onDrop($event, category.id.toString(), category.services.length)">
              <div class="header-left flex items-center justify-between w-full rounded-2xl">
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    [checked]="isCategoryFullySelected(category)"
                    (change)="toggleCategorySelection(category, $event)"
                    class="form-checkbox mr-2"
                  >
                  <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon"></app-social-icon>
                  <label class="ms-2 text-sm font-medium">
                    <!-- Use translate pipe for All Platforms and All Categories -->
                    <!-- <ng-container *ngIf="category.isAllPlatforms">
                      {{ 'filter.all_platforms' | translate }}
                    </ng-container>
                    <ng-container *ngIf="!category.isAllPlatforms">
                      {{ category.platformName }}
                    </ng-container>
                    | -->
                    <ng-container *ngIf="category.isAllCategories">
                      {{ 'filter.all_categories' | translate }}
                    </ng-container>
                    <ng-container *ngIf="!category.isAllCategories">
                      {{ category.name }}
                    </ng-container>
                  </label>
                  <div class="relative ml-2">
                    <app-admin-menu
                      [menuItems]="getCategoryMenuItems(category)"
                      (menuItemClicked)="onCategoryMenuItemClick($event, category)">
                      <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                    </app-admin-menu>
                  </div>
                </div>
                <div class="flex items-center">

                  <div class="relative inline-block menu-container">
                    <button
                      (click)="toggleCategoryPlatformDropdown($event, category)"
                      class="bg-blue-500 text-white text-xs px-3 py-1 rounded-full mr-2">
                      {{ category.platformName }}
                    </button>

                    <!-- Platform Dropdown Menu -->
                    <div *ngIf="showCategoryPlatformDropdown && selectedCategoryForPlatform?.id === category.id"
                         class="absolute w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 platform-dropdown-content mt-1">
                      <div class="py-2 max-h-60 overflow-y-auto">
                        <div *ngFor="let platform of allPlatforms"
                             class="px-4 py-2 hover:bg-gray-100 flex items-center cursor-pointer"
                             (click)="selectCategoryPlatform(category, platform)">
                          <span class="inline-flex items-center justify-center w-5 h-5 mr-3">
                            <span *ngIf="isCategoryPlatformSelected(category, platform.id)" class="text-blue-500">✓</span>
                          </span>
                          <app-social-icon *ngIf="platform.icon" [icon]="platform.icon"></app-social-icon>
                          <span class="ml-2 text-sm text-gray-700">{{ platform.name }}</span>
                        </div>
                      </div>
                      <div class="border-t border-gray-200">
                        <button
                          (click)="addNewPlatformFromCategory($event)"
                          class="w-full text-left px-4 py-3 text-blue-500 hover:bg-gray-100 flex items-center">
                          <fa-icon [icon]="['fas', 'cog']" class="mr-2"></fa-icon>
                          <span class="text-sm">Configure</span>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="category-drag-handle mr-2 cursor-grab">
                    <fa-icon [icon]="['fas', 'arrows-alt']" class="text-gray-400"></fa-icon>
                  </div>
                  <div class="category-visibility-toggle">
                    <button class="toggle-arrow-btn" (click)="onToggleCategoryVisibility(category, category.hide)">
                      <fa-icon
                        [icon]="['fas', category.hide ? 'angle-down' : 'angle-up']"
                        class="text-gray-600 text-xl"
                      ></fa-icon>
                    </button>
                  </div>
                </div>
              </div>
            </td>

            <!-- Custom drag preview for category header -->
            <template class="category-drag-preview">
              <table class="category-preview-table">
                <tr class="category-header-row-preview">
                  <td colspan="9">
                    <div class="header-left flex items-center justify-between w-full rounded-2xl p-3">
                      <div class="flex items-center">
                        <div class="w-4 h-4 mr-2"></div>
                        <app-social-icon *ngIf="category.platformIcon" [icon]="category.platformIcon"></app-social-icon>
                        <span class="ms-2 text-sm font-medium">
                          <ng-container *ngIf="category.isAllCategories">
                            {{ 'filter.all_categories' | translate }}
                          </ng-container>
                          <ng-container *ngIf="!category.isAllCategories">
                            {{ category.name }}
                          </ng-container>
                        </span>
                      </div>
                      <div class="flex items-center">
                        <span class="bg-blue-500 text-white text-xs px-3 py-1 rounded-full mr-2">
                          {{ category.platformName }}
                        </span>
                        <div class="category-drag-handle mr-2">
                          <fa-icon [icon]="['fas', 'arrows-alt']" class="text-gray-400"></fa-icon>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </template>
          </tr>

          <!-- Category Services -->
          <ng-container *ngIf="!category.hide">
            <tbody class="category-services"
                   cdkDropList
                   [cdkDropListData]="category.services"
                   (cdkDropListDropped)="onServiceDropCdk($event, category)">
              <tr *ngFor="let service of category.services; let i = index"
                class="bg-white service-row"
                [ngClass]="{'deactivated': !isServiceEnabled(service)}"
                cdkDrag
                [cdkDragData]="service"
                (cdkDragStarted)="onServiceDragStarted($event)">
                <td class="text-center">
                  <input
                    type="checkbox"
                    [checked]="isServiceSelected(service)"
                    (change)="toggleServiceSelection(service, $event)"
                    class="form-checkbox"
                  >
                </td>
                <td class="px-4 py-2">
                  <div class="p-2">
                    <!-- Service info -->
                    <div class="flex items-center">
                      <div class="service-drag-handle mr-3 cursor-grab" cdkDragHandle>
                        <fa-icon [icon]="['fas', 'grip-vertical']" class="text-gray-400"></fa-icon>
                      </div>
                      <app-service-label  [service]="service"></app-service-label>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="api-info-column">
                    <div class="api-id-container">
                      <span class="api-id">{{ service.api_service_id }}</span>
                      <fa-icon *ngIf="service.api_service_id" [icon]="['fas', 'copy']" class="copy-icon" (click)="copyToClipboard(service.api_service_id, $event)" [fixedWidth]="true"></fa-icon>
                    </div>
                    <div class="api-url-container">
                      <a *ngIf="service.api_provider?.url" [href]="'https://' + extractDomainName(service.api_provider.url)" target="_blank" class="api-url">{{ extractDomainName(service.api_provider.url) }}</a>
                      <span *ngIf="!service.api_provider?.url" class="api-url">No Provider</span>
                      <!-- <fa-icon [icon]="['fas', 'copy']" class="copy-icon" (click)="copyToClipboard(extractDomainName(service.api_provider.url), $event)" [fixedWidth]="true"></fa-icon> -->
                    </div>
                  </div>
                </td>
                <td>{{ service.min }}</td>
                <td>{{ service.max }}</td>

                <td>
                  <span *ngIf="service.refill === true; else elseBlock" class="lifetime-refill-badge">{{service.refill_days}} days</span>
                  <ng-template #elseBlock>
                    <span  class="lifetime-refill-badge">No refill</span>
                  </ng-template>
                </td>
                <td>{{ service.average_time | timeFormat }} </td>
                <td>
                <div class="relative">
                    <app-admin-menu
                      [menuItems]="getServiceMenuItems(service)"
                      (menuItemClicked)="onServiceMenuItemClick($event, service)">
                      <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                    </app-admin-menu>
                  </div>
                  </td>

                  <!-- CDK drag preview for service row -->
                  <div *cdkDragPreview class="service-row-preview">
                    <table class="service-preview-table">
                      <tr class="bg-white service-row-preview" [ngClass]="{'deactivated': !isServiceEnabled(service)}">
                        <td class="text-center">
                          <div class="w-4 h-4 mx-auto"></div>
                        </td>
                        <td class="px-4 py-2">
                          <div class="p-2">
                            <div class="flex items-center">
                              <div class="service-drag-handle mr-3 cursor-grab">
                                <fa-icon [icon]="['fas', 'grip-vertical']" class="text-gray-400"></fa-icon>
                              </div>
                              <app-service-label [service]="service"></app-service-label>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div class="api-info-column">
                            <div class="api-id-container">
                              <span class="api-id">{{ service.api_service_id }}</span>
                            </div>
                            <div class="api-url-container">
                              <span *ngIf="service.api_provider?.url" class="api-url">{{ extractDomainName(service.api_provider.url) }}</span>
                              <span *ngIf="!service.api_provider?.url" class="api-url">No provider</span>
                            </div>
                          </div>
                        </td>
                        <td>{{ service.min }}</td>
                        <td>{{ service.max }}</td>
                        <td>
                          <span *ngIf="service.refill === true" class="lifetime-refill-badge">{{service.refill_days}} days</span>
                          <span *ngIf="service.refill !== true" class="lifetime-refill-badge">No refill</span>
                        </td>
                        <td>{{ service.average_time | timeFormat }}</td>
                        <td></td>
                      </tr>
                    </table>
                  </div>
              </tr>
            </tbody>

          </ng-container>
        </ng-container>
      </ng-container>





    </tbody>
  </table>
</div>

<!-- Card View (for mobile) -->
<div *ngIf="viewMode === 'card'" class="mb-6">
  <!-- Select All and Bulk Actions Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 p-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <input
          type="checkbox"
          [checked]="areAllServicesSelected()"
          (change)="toggleAllServices($event)"
          class="form-checkbox !mr-2"
        >
        <span class="font-medium">
          <ng-container *ngIf="hasSelectedServices(); else selectAllLabel">
            {{ getSelectedServicesCount() }} {{ 'selected' | translate }}
          </ng-container>
          <ng-template #selectAllLabel>
            {{ 'Select All' | translate }}
          </ng-template>
        </span>
      </div>

      <!-- Bulk Action Menu Button -->
      <div class="relative menu-container" *ngIf="hasSelectedServices()">
        <!-- Bulk Action Menu using app-admin-menu component -->
        <app-admin-menu
          [menuItems]="getBulkActionMenuItems()"
          [isOpen]="showBulkActionMenu"
          (menuItemClicked)="onBulkActionMenuItemClick($event)"
          (menuClosed)="closeBulkActionMenu()">
          <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
        </app-admin-menu>
      </div>
    </div>
  </div>


</div>

</div>

<app-new-service
  *ngIf="showNewServiceModal"
  [serviceType]="uiStateService.selectedServiceType"
  [serviceToEdit]="uiStateService.selectedServiceForAction"
  (close)="closeNewServiceModal()"
  (serviceAdded)="onServiceAdded($event)"
  (serviceUpdated)="onServiceAdded($event)"
  (openResources)="openResourcesFromNewService()">
</app-new-service>

<app-add-platform-light
  *ngIf="showAddPlatformLightModal"
  (close)="closeAddPlatformLightModal()"
  (platformAdded)="onPlatformAdded($event)">
</app-add-platform-light>

<app-platform-management
  *ngIf="showPlatformManagementModal"
  (close)="closePlatformManagementModal()"
  (platformsUpdated)="onPlatformsUpdated($event)">
</app-platform-management>

<app-import-services
  *ngIf="showImportServicesModal"
  (close)="closeImportServicesModal()"
  (servicesImported)="onServicesImported($event)">
</app-import-services>

<app-category-selection
  *ngIf="showCategorySelectionModal"
  [serviceId]="selectedServiceForAction ? selectedServiceForAction.id : null"
  (close)="closeCategorySelectionModal()"
  (categorySelected)="onCategorySelectedForMove($event)">
</app-category-selection>

<app-new-category
  *ngIf="showNewCategoryModal"
  [categoryToEdit]="categoryToEdit"
  (close)="closeNewCategoryModal()"
  (categoryAdded)="onCategoryAdded($event)"
  (categoryUpdated)="onCategoryUpdated($event)">
</app-new-category>

<app-new-prices
  *ngIf="showNewPricesModal"
  [selectedServices]="selectionService.getSelectedServices()"
  (close)="closeNewPricesModal()"
  (pricesUpdated)="onPricesUpdated($event)">
</app-new-prices>

<app-new-special-prices
  *ngIf="showNewSpecialPricesModal"
  [selectedServices]="selectionService.getSelectedServices()"
  [selectedService]="selectedServiceForAction"
  [editMode]="!!specialPriceToEdit"
  [specialPriceToEdit]="specialPriceToEdit"
  (close)="closeNewSpecialPricesModal()"
  (specialPriceAdded)="onSpecialPriceAdded($event)">
</app-new-special-prices>

<app-special-prices-user
  *ngIf="showSpecialPricesUserModal"
  [selectedServices]="selectionService.getSelectedServices()"
  [selectedService]="selectedServiceForAction"
  (close)="closeSpecialPricesUserModal()"
  (specialPriceAdded)="onSpecialPriceAdded($event)">
</app-special-prices-user>

<app-special-prices-service
  *ngIf="showSpecialPricesServiceModal"
  [selectedService]="selectedServiceForAction"
  (close)="closeSpecialPricesServiceModal()"
  (specialPriceAdded)="onSpecialPriceAdded($event)">
</app-special-prices-service>

<app-resources
  *ngIf="showResourcesModal"
  (close)="closeResourcesModal()"
  (openNewService)="openNewServiceFromResources($event)">
</app-resources>

<!-- Delete Confirmation Modal -->
<app-delete-confirmation
  *ngIf="showDeleteConfirmation && serviceToDelete"
  [itemName]="serviceToDelete.name"
  [isLoading]="isDeleting"
  (close)="closeDeleteConfirmation()"
  (confirm)="confirmDeleteService()">
</app-delete-confirmation>
